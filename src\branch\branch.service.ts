import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Region } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { CreateBranchDto } from './dto/create-branch.dto';
import { DeleteBranchDto } from './dto/delete-branch.dto';
import { UpdateBranchDto } from './dto/update-branch.dto';

@Injectable()
export class BranchService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly taxJurisdictionService: TaxJurisdictionService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findBranch({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const branch = await this.databaseService.branch.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });
    return branch;
  }

  async findBranchByName(name: string, companyId: string) {
    const branch = await this.databaseService.branch.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });
    return branch;
  }

  async findBranchByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const branch = await this.databaseService.branch.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });
    return branch;
  }

  async findBranchById(id: string, companyId: string) {
    const branch = await this.databaseService.branch.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });
    return branch;
  }

  // Method to find a role by ID or Name
  async getBranches(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;
    const branches = await this.databaseService.branch.findMany({
      where: {
        companyId,
        status: {
          not: 'INACTIVE',
        },
      },
      include: {
        taxJurisdiction: {
          select: {
            name: true,
          },
        },
        region: {
          select: {
            name: true,
          },
        },
      },
    });

    return branches.map((branch) => ({
      ...branch,
      name: branch.name.split('|')[1],
      taxJurisdiction: branch.taxJurisdiction?.name.split('|')[1],
      region: branch.region?.name.split('|')[1],
    }));
  }

  // Method to create a new role
  async acceptBranchAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    let existingRegion: Region | null = null;
    switch (action) {
      case 'CREATE': {
        const { name, taxJurisdiction, description, region } = JSON.parse(
          queue.data,
        ) as CreateBranchDto;

        // Get tax jurisdiction ID (validation already done at request level)
        const existingTaxJurisdiction =
          await this.taxJurisdictionService.findTaxJurisdictionByName(
            taxJurisdiction,
            companyId,
          );

        // Get region ID if provided (validation already done at request level)
        if (region) {
          existingRegion = await this.databaseService.region.findFirst({
            where: {
              companyId,
              name: `${companyId}|${region}`,
            },
          });
        }

        await this.databaseService.branch.create({
          data: {
            name: `${companyId}|${name}`,
            companyId,
            regionId: existingRegion?.id,
            taxJurisdictionId: existingTaxJurisdiction!.id,
            description,
            approvedBy,
            createdBy: requestedBy,
          },
        });

        return true;
      }

      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateBranchDto;

        await this.updateBranch({
          companyId,
          payload,
        });

        return true;
      }

      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteBranchDto;

        await this.deleteBranch(payload);

        return true;
      }
      default:
        return false;
    }
  }

  async createBranch({
    payload,
    token,
  }: {
    payload: CreateBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { taxJurisdiction, name, description, region } = payload;

    if (!taxJurisdiction || !name) {
      throw new BadRequestException('Missing required field');
    }

    // Check if taxJurisdiction exists
    const existingTaxJurisdiction =
      await this.taxJurisdictionService.findTaxJurisdictionByName(
        taxJurisdiction,
        decodedToken.companyId,
      );

    if (!existingTaxJurisdiction) {
      throw new NotFoundException('Tax Jurisdiction does not exist');
    }

    // Check if region exists (if provided)
    if (region) {
      const existingRegion = await this.databaseService.region.findFirst({
        where: {
          companyId: decodedToken.companyId,
          name: `${decodedToken.companyId}|${region}`,
        },
      });

      if (!existingRegion) {
        throw new NotFoundException('Region does not exist');
      }
    }

    // Check if the branch already exists by name
    const existingBranchName = await this.findBranchByName(
      name,
      decodedToken.companyId,
    );

    if (existingBranchName) {
      throw new ConflictException('Branch already exists');
    }

    await this.authorizationRequestMaker.queueRequest({
      payload: {
        taxJurisdiction,
        name,
        description,
        region,
      },
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.BRANCH,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateBranchRequest({
    payload,
    token,
  }: {
    payload: UpdateBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the branch exists
    const existingBranch = await this.findBranchById(
      payload.id,
      decodedToken.companyId,
    );

    if (!existingBranch) {
      throw new NotFoundException('Branch not found.');
    }

    // Check if taxJurisdiction exists
    if (payload.taxJurisdiction) {
      const existingTaxJurisdiction =
        await this.taxJurisdictionService.findTaxJurisdictionByName(
          payload.taxJurisdiction,
          decodedToken.companyId,
        );

      if (!existingTaxJurisdiction) {
        throw new NotFoundException('Tax Jurisdiction does not exist');
      }
    }

    // Check if region exists (if provided)
    if (payload.region) {
      const existingRegion = await this.databaseService.region.findFirst({
        where: {
          companyId: decodedToken.companyId,
          name: `${decodedToken.companyId}|${payload.region}`,
        },
      });

      if (!existingRegion) {
        throw new NotFoundException('Region does not exist');
      }
    }

    // Check if another branch exists with the same name (excluding current one)
    if (payload.name) {
      const existingBranchName = await this.findBranchByNameExcludingId(
        payload.name,
        decodedToken.companyId,
        payload.id,
      );

      if (existingBranchName) {
        throw new ConflictException('Branch already exists');
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.BRANCH,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateBranch({
    payload,
    companyId,
  }: {
    payload: UpdateBranchDto;
    companyId: string;
  }) {
    const { description, taxJurisdiction, name, id, region } = payload;

    const updateData: any = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    if (taxJurisdiction) {
      const existingTaxJurisdiction =
        await this.taxJurisdictionService.findTaxJurisdictionByName(
          taxJurisdiction,
          companyId,
        );
      updateData.taxJurisdictionId = existingTaxJurisdiction!.id;
    }

    if (region) {
      const regionExist = await this.databaseService.region.findFirst({
        where: {
          companyId,
          name: `${companyId}|${region}`,
        },
      });
      updateData.regionId = regionExist!.id;
    }

    await this.databaseService.branch.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteBranchRequest({
    payload,
    token,
  }: {
    payload: DeleteBranchDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the branch exists
    const branchExist = await this.findBranchById(
      payload.id,
      decodedToken.companyId,
    );

    if (!branchExist) {
      throw new NotFoundException('Branch not found');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload: { ...payload, name: branchExist.name.split('|')[1] },
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.BRANCH,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async deleteBranch(payload: DeleteBranchDto) {
    const { id } = payload;

    await this.databaseService.branch.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
