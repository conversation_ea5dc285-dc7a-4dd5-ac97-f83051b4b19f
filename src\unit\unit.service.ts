import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Region } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { RegionService } from 'src/region/region.service';
import { TaxJurisdictionService } from 'src/tax-jurisdiction/tax-jurisdiction.service';
import { CreateUnitDto } from './dto/create-unit.dto';
import { DeleteUnitDto } from './dto/delete-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';

@Injectable()
export class UnitService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
    private readonly regionService: RegionService,
    private readonly taxJurisdictionService: TaxJurisdictionService,
  ) {}

  async findUnit({ identifier, token }: { identifier: string; token: string }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const unit = await this.databaseService.unit.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return unit;
  }

  async findUnitById(id: string, companyId: string) {
    const unit = await this.databaseService.unit.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return unit;
  }

  async findUnitByName(name: string, companyId: string) {
    const unit = await this.databaseService.unit.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return unit;
  }

  async findUnitByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const unit = await this.databaseService.unit.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return unit;
  }

  // Method to find a role by ID or Name
  async getUnits(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { companyId } = decodedToken;
    const units = await this.databaseService.unit.findMany({
      where: {
        companyId,
      },
      include: {
        taxJurisdiction: {
          select: {
            name: true,
          },
        },
        region: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            employee: true,
          },
        },
      },
    });

    return units.map((unit) => ({
      ...unit,
      name: unit.name.split('|')[1],
      taxJurisdiction: unit.taxJurisdiction?.name.split('|')[1],
      region: unit.region?.name.split('|')[1],
    }));
  }

  // Method to create a new role
  async acceptUnitAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        let regionExist: Region | null = null;
        const { name, description, taxJurisdiction, region } = JSON.parse(
          queue.data,
        ) as CreateUnitDto;

        // Get region ID if provided (validation already done at request level)
        if (region) {
          regionExist = await this.regionService.findRegionByName(
            region,
            companyId,
          );
        }

        // Get tax jurisdiction ID (validation already done at request level)
        const taxJurisdictionExist =
          await this.taxJurisdictionService.findTaxJurisdictionByName(
            taxJurisdiction,
            companyId,
          );

        await this.databaseService.unit.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            taxJurisdictionId: taxJurisdictionExist!.id,
            regionId: regionExist?.id,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateUnitDto;

        await this.updateUnit({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteUnitDto;

        await this.deleteUnit(payload);

        return true;
      }

      default:
        return false;
    }
  }

  async createUnit({
    payload,
    token,
  }: {
    payload: CreateUnitDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const { taxJurisdiction, name, description, region } = payload;

    if (!taxJurisdiction || !name) {
      throw new BadRequestException('Missing required field');
    }

    // Check if tax jurisdiction exists
    const existingTaxJurisdiction =
      await this.taxJurisdictionService.findTaxJurisdictionByName(
        taxJurisdiction,
        decodedToken.companyId,
      );

    if (!existingTaxJurisdiction) {
      throw new NotFoundException('Tax Jurisdiction does not exist');
    }

    // Check if region exists (if provided)
    if (region) {
      const existingRegion = await this.regionService.findRegionByName(
        region,
        decodedToken.companyId,
      );

      if (!existingRegion) {
        throw new NotFoundException('Region does not exist');
      }
    }

    // Check if the unit already exists by name
    const existingUnit = await this.findUnitByName(
      name,
      decodedToken.companyId,
    );

    if (existingUnit) {
      throw new ConflictException('Unit already exists');
    }

    await this.authorizationRequestMaker.queueRequest({
      payload: { taxJurisdiction, name, description, region },
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.UNIT,
      requestedBy: decodedToken.name,
    });
  }

  async updateUnitRequest({
    payload,
    token,
  }: {
    payload: UpdateUnitDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the unit exists
    const existingUnit = await this.findUnitById(
      payload.id,
      decodedToken.companyId,
    );

    if (!existingUnit) {
      throw new NotFoundException('Unit not found.');
    }

    // Check if another unit exists with the same name (excluding current one)
    if (payload.name) {
      const existingUnitName = await this.findUnitByNameExcludingId(
        payload.name,
        decodedToken.companyId,
        payload.id,
      );

      if (existingUnitName) {
        throw new ConflictException('Unit already exists');
      }
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.update,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.UNIT,
      requestedBy: decodedToken.name,
    });
  }

  async updateUnit({
    payload,
    companyId,
  }: {
    payload: UpdateUnitDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: any = {
      description: description || name,
    };

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    await this.databaseService.unit.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteUnitRequest({
    payload,
    token,
  }: {
    payload: DeleteUnitDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the unit exists
    const existingUnit = await this.findUnitById(
      payload.id,
      decodedToken.companyId,
    );

    if (!existingUnit) {
      throw new NotFoundException('Unit not found.');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.delete,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.UNIT,
      requestedBy: decodedToken.name,
    });
  }

  async deleteUnit(payload: DeleteUnitDto) {
    const { id } = payload;

    await this.databaseService.unit.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
