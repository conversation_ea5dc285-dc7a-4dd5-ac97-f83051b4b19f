import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateGradeDto } from './dto/create-grade.dto';
import { DeleteGradeDto } from './dto/delete-grade.dto';
import { UpdateGradeDto } from './dto/update-grade.dto';

@Injectable()
export class GradeService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findGrade({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const grade = await this.databaseService.jobGrade.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return grade;
  }
  async findGradeById(id: string, companyId: string) {
    const grade = await this.databaseService.jobGrade.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return grade;
  }

  async findGradeByName(name: string, companyId: string) {
    const grade = await this.databaseService.jobGrade.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return grade;
  }

  async findGradeByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const grade = await this.databaseService.jobGrade.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return grade;
  }

  async getGrade(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    try {
      const { companyId } = decodedToken;
      const grades = await this.databaseService.jobGrade.findMany({
        where: {
          companyId,
          status: {
            not: 'INACTIVE',
          },
        },
      });

      console.log(grades);

      return grades.map((grade) => ({
        ...grade,
        name: grade.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptGradeAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const { name, description } = JSON.parse(queue.data) as CreateGradeDto;

        await this.databaseService.jobGrade.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case 'UPDATE': {
        const payload = JSON.parse(queue.data) as UpdateGradeDto;

        await this.updateGrade({
          companyId,
          payload,
        });

        return true;
      }

      case 'DELETE': {
        const payload = JSON.parse(queue.data) as DeleteGradeDto;

        await this.deleteGrade(payload);

        return true;
      }
      default:
        return false;
    }
  }

  async createGrade({
    payload,
    token,
  }: {
    payload: CreateGradeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the grade already exists by name
    const gradeExist = await this.findGradeByName(
      payload.name,
      decodedToken.companyId,
    );

    if (gradeExist) {
      throw new ConflictException('Grade already exists');
    }

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.GRADE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateGradeRequest({
    payload,
    token,
  }: {
    payload: UpdateGradeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the grade exists
    const existingGrade = await this.databaseService.jobGrade.findUnique({
      where: {
        id: payload.id,
        companyId: decodedToken.companyId,
      },
    });

    if (!existingGrade) {
      throw new NotFoundException('Grade not found.');
    }

    // Check if another grade exists with the same name (excluding current one)
    if (payload.name) {
      const existingGradeName = await this.findGradeByNameExcludingId(
        payload.name,
        decodedToken.companyId,
        payload.id,
      );

      if (existingGradeName) {
        throw new ConflictException('Grade already exists');
      }
    }

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.GRADE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateGrade({
    payload,
    companyId,
  }: {
    payload: UpdateGradeDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: Prisma.JobGradeUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    await this.databaseService.jobGrade.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteGradeRequest({
    payload,
    token,
  }: {
    payload: DeleteGradeDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const gradeExist = await this.findGradeById(
      payload.id,
      decodedToken.companyId,
    );

    if (!gradeExist) {
      throw new NotFoundException('Grade not found.');
    }

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload: { ...payload, name: gradeExist.name.split('|')[1] },
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.GRADE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async deleteGrade(payload: DeleteGradeDto) {
    const { id } = payload;

    await this.databaseService.jobGrade.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }
}
