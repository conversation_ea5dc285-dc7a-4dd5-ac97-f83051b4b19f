generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id        Int      @id @default(autoincrement())
  name      String   @db.Var<PERSON>har(255)
  email     String   @unique @db.Var<PERSON>har(255)
  password  String
  slug      String   @unique @db.VarChar(255)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  roleId    Int
  role      Role      @relation(fields: [roleId], references: [id])
  companies Company[]

  users User[]

  status Status @default(ACTIVE)
}

// Enrollment Table
model Enrollment {
  id String @id @default(uuid())

  email String @db.VarChar(255) // Company email (same as user's root email)

  // Company Info
  phoneNumber        String  @db.VarChar(20)
  uniqueRef          String  @unique @db.VarChar(50)
  name               String  @db.VarChar(100)
  logo               String? @db.VarChar(255)
  address            String  @db.VarChar(255)
  state              String  @db.VarChar(50)
  city               String  @db.VarChar(50)
  zipCode            String? @db.VarChar(20)
  website            String? @db.VarChar(255)
  description        String? @db.VarChar(500)
  registrationNumber String? @db.VarChar(50)
  industry           String? @db.VarChar(50)
  industryType       String? @db.VarChar(50)
  country            String  @db.VarChar(50)

  // Root User Info
  password     String
  accountName  String
  accountEmail String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Company {
  id        String @id @default(uuid())
  accountId Int

  email              String               @db.VarChar(255)
  emailVerified      Boolean              @default(false)
  phoneNumber        String
  name               String               @db.VarChar(100)
  logo               String?              @db.VarChar(255)
  address            String               @db.VarChar(255)
  state              String               @db.VarChar(50)
  city               String               @db.VarChar(50)
  zipCode            String?              @db.VarChar(20)
  website            String?              @db.VarChar(255)
  description        String?              @db.VarChar(500)
  registrationNumber String?              @db.VarChar(50)
  industry           String?              @db.VarChar(50)
  industryType       String?              @db.VarChar(50)
  accountPrefix      String?
  accountClass       String?              @default("REGULAR_SAV_TIER_1")
  roles              Role[]
  country            String               @db.VarChar(50)
  status             String               @default("ACTIVE") // Indicates if the company is active or inactive
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  users              User[]
  AuthorizationQueue AuthorizationQueue[]
  branches           Branch[]
  gradeLevel         GradeLevel[]
  grade              JobGrade[]
  ContractType       ContractType[]
  SalaryPackage      SalaryPackage[]
  Department         Department[]
  JobTitle           JobTitle[]
  Unit               Unit[]
  Allowance          Allowance[]
  Deduction          Deduction[]
  PayrollUpload      PayrollUpload[]
  employee           Employee[]
  BulkEmployeeJob    BulkEmployeeJob[]
  Account            Account              @relation(fields: [accountId], references: [id], onDelete: Cascade)
  taxJurisdictions   TaxJurisdiction[]
  regions            Region[]
  SubBranch          SubBranch[]

  @@unique([accountId, email])
  @@index([accountId, email])
}

model Branch {
  id String @id @default(uuid())

  name        String  @unique @db.VarChar(255)
  description String? @db.VarChar(255)

  companyId String
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  department    Department[]
  unit          Unit[]
  employee      Employee[]
  PayrollUpload PayrollUpload[]
  UserBranch    UserBranch[]

  taxJurisdiction   TaxJurisdiction? @relation(fields: [taxJurisdictionId], references: [id], onDelete: Cascade)
  taxJurisdictionId String?

  regionId String?
  region   Region? @relation(fields: [regionId], references: [id], onDelete: Cascade)

  LocationLga   LocationLga? @relation(fields: [locationLgaId], references: [id], onDelete: Cascade)
  locationLgaId String?

  status    Status      @default(ACTIVE)
  SubBranch SubBranch[]

  @@unique([name, companyId])
  @@index([name, companyId, taxJurisdictionId])
  @@index([name, companyId])
}

model SubBranch {
  id String @id @default(uuid())

  name        String  @unique
  description String? @db.VarChar(255)

  branchId String
  branch   Branch @relation(fields: [branchId], references: [id], onDelete: Cascade)

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  status   Status     @default(ACTIVE)
  Employee Employee[]

  @@unique([name, companyId, branchId])
  @@index([name, companyId, branchId])
}

model TaxJurisdiction {
  id String @id @default(uuid())

  name        String  @unique @db.VarChar(255)
  description String? @db.VarChar(255)

  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  branch Branch[]
  unit   Unit[]

  status Status @default(ACTIVE)

  @@unique([name, companyId])
  @@index([name, companyId])
}

model Region {
  id String @id @default(uuid())

  name        String  @unique @db.VarChar(255)
  description String? @db.VarChar(255)

  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  department Department[]
  unit       Unit[]

  status Status   @default(ACTIVE)
  Branch Branch[]

  @@unique([name, companyId])
  @@index([name, companyId])
}

model User {
  id String @id @default(uuid())

  name             String? @db.VarChar(255)
  email            String  @db.VarChar(255)
  password         String
  twoFactorEnabled Boolean @default(false)

  isRoot Boolean @default(false) // Indicates if the user is a root user

  hasAccessToAllBranches Boolean @default(false)

  branches  UserBranch[]
  locations UserLocation[]

  companyId String
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  roleId Int
  role   Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  status String @default("PENDING")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  accountId Int?
  Account   Account? @relation(fields: [accountId], references: [id])

  @@unique([email, companyId]) // Ensures unique email per company
  @@index([companyId]) // Index for faster lookups
  @@index([roleId]) // Index for faster lookups
}

model UserBranch {
  id       String @id @default(uuid())
  userId   String
  branchId String

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  branch Branch @relation(fields: [branchId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([userId, branchId])
}

model UserLocation {
  id         String @id @default(uuid())
  userId     String
  locationId String

  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  location Location @relation(fields: [locationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@unique([userId, locationId])
}

model Role {
  id Int @id @default(autoincrement())

  name          String          @unique @db.VarChar(350) // Role names typically fit within 50 chars
  description   String?         @db.VarChar(250)
  RolePrivilege RolePrivilege[]
  isPublic      Boolean         @default(false) // true = system-created/shared

  companyId String?
  company   Company? @relation(fields: [companyId], references: [id], onDelete: Cascade)

  createdBy  String @db.VarChar(100) // Name or identifier of the creator
  approvedBy String @default("SYSTEM") @db.VarChar(100) // Name or identifier of the approver

  status Status @default(ACTIVE)

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  User      User[]
  Account   Account[]
}

model Privilege {
  id String @id @default(uuid())

  name     String       @unique // usually same as the action name
  actionId String       @unique
  action   EntityAction @relation(fields: [actionId], references: [id], onDelete: Cascade)

  rolePrivilege RolePrivilege[]

  isPrivate Boolean @default(false) // Indicates if the privilege is private
  status    Status  @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model RolePrivilege {
  privilege     Privilege @relation(fields: [privilegeName], references: [name], onDelete: Cascade)
  privilegeName String
  role          Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  roleId        Int
  assignedAt    DateTime  @default(now())

  @@id([roleId, privilegeName])
}

model BusinessEntity {
  id String @id @default(uuid())

  name            String            @unique @db.VarChar(100) // e.g. "STAFF"
  description     String?           @db.VarChar(200) // 
  actions         EntityAction[]
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  WorkflowRequest WorkflowRequest[]
}

model EntityAction {
  id String @id @default(uuid())

  name            String            @unique @db.VarChar(100) // e.g. "STAFF_CREATE"
  entityId        String
  entity          BusinessEntity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  workflowRequest WorkflowRequest[]

  Privilege Privilege?
}

model WorkflowRequest {
  id String @id @default(uuid())

  entityId    String
  actionId    String
  requestedBy Int // Account/User who triggered it
  status      RequestStatus @default(PENDING)
  metadata    Json // optional payload (e.g., staff details)

  entity BusinessEntity @relation(fields: [entityId], references: [id])
  action EntityAction   @relation(fields: [actionId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Employee {
  id                   String  @id @default(uuid())
  title                String
  staffCode            String  @unique
  firstName            String
  lastName             String
  middleName           String?
  birthday             String
  placeOfBirth         String?
  religion             String?
  taxId                String?
  pensionId            String?
  pfa                  String?
  dateAppointedToLevel String?

  // Group: Personal Info
  gender        String
  maritalStatus String
  nationality   String
  stateOfOrigin String
  localGovt     String?

  residentialAddress   String
  residentialLocalGovt String?
  residentialState     String
  residentialCountry   String

  // Group: Contact
  phone1 String
  phone2 String?
  email  String?

  // Group: Next of Kin
  nextOfKinFullName     String?
  nextOfKinRelationship String?
  nextOfKinPhoneNumber  String?
  nextOfKinEmail        String?
  nextOfKinAddress      String?

  // Group: Education
  highestQualification String?
  course               String?
  institutionName      String?
  institutionAddress   String?
  dateOfGraduation     String?

  dateEmployed String?

  // Group: Identity
  bvn         String?
  nin         String?
  kycVerified Boolean @default(false)

  // Group: Spouse & Children
  nameOfSpouse String?
  noOfChildren String?

  // Group: Bank Info
  bankName      String? @default("Corestep MFB")
  bankSortCode  String?
  accountNumber String?
  accountStatus String  @default("PENDING")

  // Group: Guarantor
  passport                       String?
  certificate                    String?
  guarantorPassport              String?
  guarantorFullname              String?
  guarantorPhoneNumber           String?
  guarantorRelationShip          String?
  guarantorAddress               String?
  guarantorOccupation            String?
  guarantorMeansOfIdentification String?

  // Group: Work
  companyId       String
  company         Company @relation(fields: [companyId], references: [id])
  grossSalary     String?
  tax             String?
  otherDeductions String?
  currency        String?

  status    Status   @default(ACTIVE)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  gradelevel     GradeLevel? @relation(fields: [gradeLevelName], references: [name])
  gradeLevelName String?

  branchName String
  Branch     Branch @relation(fields: [branchName], references: [name])

  subBranchName String?
  subBranch     SubBranch? @relation(fields: [subBranchName], references: [name])

  JobTitle     JobTitle @relation(fields: [jobTitleName], references: [name])
  jobTitleName String

  unit     Unit?   @relation(fields: [unitName], references: [name])
  unitName String?

  createdBy    String    @db.VarChar(100)
  approvedBy   String?
  JobGrade     JobGrade? @relation(fields: [jobGradeName], references: [name])
  jobGradeName String?

  ContractType     ContractType? @relation(fields: [contractTypeName], references: [name])
  contractTypeName String?

  JobCluster     JobCluster? @relation(fields: [jobClusterName], references: [name])
  jobClusterName String?

  department     Department? @relation(fields: [departmentName], references: [name])
  departmentName String?

  salaryPackageName String
  salaryPackage     SalaryPackage? @relation(fields: [salaryPackageName], references: [name])

  @@unique([email, companyId])
}

model BulkEmployeeJob {
  id         String  @id @default(uuid())
  createdBy  String  @db.VarChar(100)
  approvedBy String?
  status     String  @default("PENDING") // PENDING | PROCESSING | COMPLETED | FAILED

  companyId     String
  company       Company  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  totalCount    Int
  successCount  Int      @default(0)
  failureCount  Int      @default(0)
  failureReason String? // general batch failure reason, if any
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  records BulkEmployeeRecord[]
}

model BulkEmployeeRecord {
  id            String   @id @default(uuid())
  jobId         String
  status        Status   @default(PENDING) // PENDING | SUCCESS | FAILED
  failureReason String?
  payload       Json // full employee data
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  job BulkEmployeeJob @relation(fields: [jobId], references: [id], onDelete: Cascade)
}

model JobTitle {
  id String @id @default(uuid())

  name      String  @unique @db.VarChar(255)
  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  description String?

  employee   Employee[]
  createdBy  String     @db.VarChar(100)
  approvedBy String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  jobCluster   JobCluster? @relation(fields: [jobClusterId], references: [id])
  jobClusterId String?

  status Status @default(ACTIVE)

  @@unique([name, companyId])
  @@index([name, companyId])
}

model ContractType {
  id String @id @default(uuid())

  name        String  @unique @db.VarChar(100) // e.g., Full-time, Part-time, Internship
  description String? @db.VarChar(255)

  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  employees Employee[] // Employees associated with this contract type

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  status Status @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([name, companyId])
  @@index([name, companyId])
}

model Unit {
  id String @id @default(uuid())

  name      String  @unique @db.VarChar(255)
  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  description String

  departmentId String?
  department   Department? @relation(fields: [departmentId], references: [id])

  branchId String?
  branch   Branch? @relation(fields: [branchId], references: [id])

  employee   Employee[]
  createdBy  String     @db.VarChar(100)
  approvedBy String?

  status Status @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  taxJurisdiction   TaxJurisdiction? @relation(fields: [taxJurisdictionId], references: [id])
  taxJurisdictionId String?

  regionId String?
  region   Region? @relation(fields: [regionId], references: [id])

  @@unique([name, companyId, departmentId])
  @@unique([name, companyId, branchId])
  @@index([name, branchId])
  @@index([name, departmentId])
  @@index([name, companyId])
}

model Department {
  id String @id @default(uuid())

  name      String  @unique @db.VarChar(255)
  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  description String

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  branchId String?
  branch   Branch? @relation(fields: [branchId], references: [id])

  jobCluster   JobCluster? @relation(fields: [jobClusterId], references: [id])
  jobClusterId String?

  units    Unit[]
  Employee Employee[]

  status Status @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Region    Region?  @relation(fields: [regionId], references: [id])
  regionId  String?

  @@unique([name, companyId, branchId])
  @@index([name, companyId, branchId])
}

model JobGrade {
  id String @id @default(uuid())

  name        String  @unique @db.VarChar(255)
  description String
  companyId   String
  company     Company @relation(fields: [companyId], references: [id])
  createdBy   String  @db.VarChar(100)
  approvedBy  String?

  Employee Employee[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  status Status @default(ACTIVE)

  @@unique([name, companyId])
  @@index([name, companyId])
}

model GradeLevel {
  id String @id @default(uuid())

  name        String  @unique @db.VarChar(255)
  description String?

  companyId  String
  company    Company  @relation(fields: [companyId], references: [id])
  createdBy  String   @db.VarChar(100)
  approvedBy String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  employee   Employee[]
  jobCluster JobCluster[]
  Allowance  Allowance[]
  Deduction  Deduction[]

  status Status @default(ACTIVE)

  @@unique([name, companyId])
  @@index([name, companyId])
}

model SalaryPackage {
  id          String  @id @default(uuid())
  name        String  @unique
  description String?

  baseSalary Float

  // Statutory Deductions
  pensionRate Float @default(0.0)
  nhfRate     Float @default(0.0)
  taxAmount   Float @default(0)

  // New Allowance Fields
  apprenticeAllowance            Float? @default(0)
  housingAllowance               Float? @default(0)
  transportAllowance             Float? @default(0)
  utilityAllowance               Float? @default(0)
  selfMaintenanceAllowance       Float? @default(0)
  hazardOrEntertainmentAllowance Float? @default(0)
  furnitureAllowance             Float? @default(0)
  fuelSubsidy                    Float? @default(0)
  domesticStaffAllowance         Float? @default(0)
  childEducationSubsidy          Float? @default(0)
  levelProficiencyAllowance      Float? @default(0)
  responsibilityAllowance        Float? @default(0)

  monthlyGrossSalary Float? @default(0)
  annualGrossSalary  Float? @default(0)

  currency String @default("NGN")
  status   Status @default(ACTIVE)

  // Relations
  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  allowances Allowance[]
  deductions Deduction[]

  createdBy  String  @db.VarChar(100)
  approvedBy String?

  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  Employee  Employee[]

  @@unique([name, companyId])
}

model Allowance {
  id           String     @id @default(uuid())
  name         String
  description  String?
  // frequency       AllowanceFrequency // e.g., monthly, yearly
  amount       Float
  // Relations
  companyId    String
  company      Company    @relation(fields: [companyId], references: [id])
  gradeLevelId String
  gradeLevel   GradeLevel @relation(fields: [gradeLevelId], references: [id])

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  createdBy  String   @db.VarChar(100)
  approvedBy String?

  SalaryPackage   SalaryPackage? @relation(fields: [salaryPackageId], references: [id])
  salaryPackageId String?

  @@unique([name, companyId, gradeLevelId])
  @@index([name, companyId, gradeLevelId])
}

model Deduction {
  id           String     @id @default(uuid())
  name         String
  description  String?
  // frequency       AllowanceFrequency // e.g., monthly, yearly
  amount       Float
  // Relations
  companyId    String
  company      Company    @relation(fields: [companyId], references: [id])
  gradeLevelId String
  gradeLevel   GradeLevel @relation(fields: [gradeLevelId], references: [id])

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  createdBy  String   @db.VarChar(100)
  approvedBy String?

  SalaryPackage   SalaryPackage? @relation(fields: [salaryPackageId], references: [id])
  salaryPackageId String?

  @@unique([name, companyId, gradeLevelId])
  @@index([name, companyId, gradeLevelId])
}

model Location {
  id   String @id @default(uuid())
  name String @unique @db.VarChar(255)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  locationLga  LocationLga[]
  UserLocation UserLocation[]
}

model LocationLga {
  id   String @id @default(uuid())
  name String @db.VarChar(255)

  locationId String
  location   Location @relation(fields: [locationId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  Branch     Branch[]

  @@unique([name, locationId])
  @@index([name, locationId])
}

model JobCluster {
  id          String  @id @default(uuid())
  name        String  @unique
  description String?

  // Optional: default grade level for jobs under this cluster
  defaultGradeLevelId String?
  defaultGradeLevel   GradeLevel? @relation(fields: [defaultGradeLevelId], references: [id])

  // Relations
  departments Department[]
  employees   Employee[]
  jobs        JobTitle[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Otp {
  id    String @id @default(uuid())
  email String @db.VarChar(255)
  code  String @db.VarChar(10)

  companyId String?
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@index([email])
}

model AuthorizationQueue {
  id String @id @default(uuid())

  requestedBy String
  data        String
  module      String
  action      String

  companyId String
  company   Company       @relation(fields: [companyId], references: [id])
  status    RequestStatus @default(PENDING)

  approvedBy String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model PayrollUpload {
  id        String  @id @default(uuid())
  companyId String
  company   Company @relation(fields: [companyId], references: [id])

  branchId String?
  branch   Branch? @relation(fields: [branchId], references: [id])

  uploadedBy        String
  approvedBy        String?
  period            String?               @db.VarChar(20) // e.g. "2024-05"
  payDate           String?               @db.VarChar(20) // e.g. "2024-05-05" 
  createdAt         DateTime              @default(now())
  records           PayrollRecordUpload[]
  payrollUploadTemp PayrollUploadTemp[]

  status Status @default(PENDING)

  @@index([companyId])
}

model PayrollRecordUpload {
  id String @id @default(uuid())

  payrollId String
  payroll   PayrollUpload @relation(fields: [payrollId], references: [id])

  staffCode                  String
  fullName                   String?
  unit                       String?
  gradeLevel                 String?
  grossPay                   Float?
  daysWorkedPreviousPayroll  Int?
  accountNumber              String?
  daysWorkedCurrentPayroll   Int?
  apprenticeAllowance        Float?
  specialCategoryAllowance   Float?
  lunchSubsidyAllowance      Float?
  monthlyBasicSalary         Float?
  housingAllowance           Float?
  transportAllowance         Float?
  utilityAllowance           Float?
  selfMaintenance            Float?
  furnitureAllowance         Float?
  hazardAllowance            Float?
  levelProficiency           Float?
  fuelSubsidy                Float?
  childEducationSubsidy      Float?
  domesticStaff              Float?
  responsibility             Float?
  managementFee              Float?
  grossPayAlt                Float?
  amortisedGross             Float?
  vehicleAmortisation        Float?
  leaveAllowance             Float?
  performanceBonus           Float?
  inconvenienceAllowance     Float?
  overTime                   Float?
  outstandingSalary          Float?
  iouRecovery                Float?
  loanSalaryAdvanceDeduction Float?
  productCashShortage        Float?
  lateness                   Float?
  absenteeism                Float?
  otherPenalty               Float?
  otherDeduction             Float?
  cooperativeContribution    Float?
  pension                    Float?
  paye                       Float?
  grossPayable               Float?
  amortisedPaye              Float?
  totalDeduction             Float?
  netPay                     Float?
  annualGross                Float?
  annualPension              Float?
  otherReliefs               Float?
  consolidatedRelief         Float?
  taxableIncome              Float?
  monthlyTax                 Float?

  createdAt DateTime @default(now())

  @@unique([payrollId, staffCode])
}

model PayrollUploadTemp {
  id String @id @default(uuid())

  payload String

  payrollUploadId String // To group multiple records from one upload
  payrollUpload   PayrollUpload @relation(fields: [payrollUploadId], references: [id])
  status          Status        @default(PENDING) // "VALID" | "INVALID" | "PENDING"
  failureReason   String?
  createdAt       DateTime      @default(now())
}

// ADMIN MODEL
model AdminUser {
  id        String   @id @default(uuid())
  name      String   @db.VarChar(255)
  email     String   @unique @db.VarChar(255)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  roleId String
}

model AdminRole {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

enum RequestStatus {
  PENDING
  APPROVED
  REJECTED
}

enum RoleScope {
  BRANCH_ONLY
  ALL_BRANCHES
}

enum Status {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING
  FAILED
  COMPLETED
  DISBURSED
}

enum AllowanceFrequency {
  monthly
  yearly
  one_time
}

// data:image/png;base64,
