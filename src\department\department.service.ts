import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue, Prisma } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { CreateDepartmentDto } from './dto/create-department.dto';
import { DeleteDepartmentDto } from './dto/delete-department.dto';
import { UpdateDepartmentDto } from './dto/update-department.dto';

@Injectable()
export class DepartmentService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findDepartmentById(id: string, companyId: string) {
    const region = await this.databaseService.department.findUnique({
      where: {
        id,
        companyId,
        status: 'ACTIVE',
      },
    });

    return region;
  }

  async findDepartment({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const department = this.databaseService.department.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return department;
  }

  async findDepartmentByName(name: string, companyId: string) {
    const department = await this.databaseService.department.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        status: 'ACTIVE',
      },
    });

    return department;
  }

  async findDepartmentByNameExcludingId(
    name: string,
    companyId: string,
    excludeId: string,
  ) {
    const department = await this.databaseService.department.findFirst({
      where: {
        name: {
          equals: `${companyId}|${name}`,
          mode: 'insensitive',
        },
        companyId,
        id: {
          not: excludeId,
        },
        status: 'ACTIVE',
      },
    });

    return department;
  }

  // Method to find a role by ID or Name
  async getDepartments(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const departments = await this.databaseService.department.findMany({
        where: {
          companyId,
        },
        include: {
          branch: {
            select: {
              name: true,
            },
          },
        },
      });

      return departments.map((department) => ({
        ...department,
        branch: department.branch ? department.branch.name.split('|')[1] : '-',
        name: department.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptDepartmentAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action as ACTIONS_CONSTANT) {
      case ACTIONS_CONSTANT.create: {
        const { name, description } = JSON.parse(
          queue.data,
        ) as CreateDepartmentDto;

        await this.databaseService.department.create({
          data: {
            name: `${companyId}|${name}`,
            description,
            companyId,
            createdBy: requestedBy,
            approvedBy,
          },
        });

        return true;
      }

      case ACTIONS_CONSTANT.update: {
        const payload = JSON.parse(queue.data) as UpdateDepartmentDto;

        await this.updateDepartment({
          companyId,
          payload,
        });

        return true;
      }
      case ACTIONS_CONSTANT.delete: {
        const payload = JSON.parse(queue.data) as DeleteDepartmentDto;

        await this.deleteDepartment(payload);

        return true;
      }
      default:
        return false;
    }
  }

  async createDepartment({
    payload,
    token,
  }: {
    payload: CreateDepartmentDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the department already exists by name
    const departmentExist = await this.findDepartmentByName(
      payload.name,
      decodedToken.companyId,
    );

    if (departmentExist) {
      throw new ConflictException('Department already exists');
    }

    return await this.authorizationRequestMaker.queueRequest({
      payload,
      action: ACTIONS_CONSTANT.create,
      companyId: decodedToken.companyId,
      module: MODULE_CONSTANT.DEPARTMENT,
      requestedBy: decodedToken.name || decodedToken.email,
    });
  }

  async updateDepartmentRequest({
    payload,
    token,
  }: {
    payload: UpdateDepartmentDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the department exists
    const existingDepartment = await this.findDepartmentById(
      payload.id,
      decodedToken.companyId,
    );

    if (!existingDepartment) {
      throw new NotFoundException(
        `Department with id ${payload.id} not found.`,
      );
    }

    // Check if another department exists with the same name (excluding current one)
    if (payload.name) {
      const existingDepartmentName = await this.findDepartmentByNameExcludingId(
        payload.name,
        decodedToken.companyId,
        payload.id,
      );

      if (existingDepartmentName) {
        throw new ConflictException('Department already exists');
      }
    }

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.update,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DEPARTMENT,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async updateDepartment({
    payload,
    companyId,
  }: {
    payload: UpdateDepartmentDto;
    companyId: string;
  }) {
    const { description, name, id } = payload;

    const updateData: Prisma.DepartmentUpdateInput = {};

    if (name) {
      updateData.name = `${companyId}|${name}`;
    }

    if (description) {
      updateData.description = description;
    }

    await this.databaseService.department.update({
      where: {
        id,
      },
      data: updateData,
    });
  }

  async deleteDepartment(payload: DeleteDepartmentDto) {
    const { id } = payload;

    await this.databaseService.department.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }

  async deleteDepartmentRequest({
    payload,
    token,
  }: {
    payload: DeleteDepartmentDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    // Check if the department exists
    const existingDepartment = await this.findDepartmentById(
      payload.id,
      decodedToken.companyId,
    );

    if (!existingDepartment) {
      throw new NotFoundException(
        `Department with id ${payload.id} not found.`,
      );
    }

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.delete,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.DEPARTMENT,
        requestedBy: decodedToken.name,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }
}
