import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Account, User } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { CryptoService } from 'src/common/crypto/crypto.service';
import { DatabaseService } from 'src/database/database.service';
import { OtpService } from 'src/otp/otp.service';
import { AuthChangePasswordDto } from './dto/auth-change-password.dto';
import { AuthCompleteForgotPasswordDto } from './dto/auth-complete-forgot-password.dto';
import { AuthInitiateForgotPasswordDto } from './dto/auth-initiate-forgot-password.dto';
import { AuthSwitchDto } from './dto/auth-switch.dto';
import { AuthUserDto } from './dto/auth-user.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly cryptoService: CryptoService,
    private jwtService: JwtService,
    private readonly authTokenService: AuthTokenService,
    private readonly otpService: OtpService,
  ) {}
  async signIn(payload: AuthUserDto) {
    const { email, password, companyId, accountId } = payload;
    let accountExist:
      | (Account & {
          role: {
            name: string;
            RolePrivilege: {
              privilegeName: string;
            }[];
          };
        })
      | null = null;

    let userExist:
      | (User & {
          role: {
            name: string;
            RolePrivilege: {
              privilegeName: string;
            }[];
          };
        })
      | null = null;

    try {
      const companyExist = await this.databaseService.company.findUnique({
        where: {
          id: companyId,
          // accountId,
        },
      });

      if (!companyExist) {
        throw new NotFoundException('Company not found');
      }

      if (!accountId) {
        accountExist = await this.databaseService.account.findUnique({
          where: {
            email,
          },
          include: {
            role: {
              select: {
                name: true,
                RolePrivilege: {
                  select: {
                    privilegeName: true,
                  },
                },
              },
            },
          },
        });

        console.log(accountExist);
      }

      if (accountId) {
        userExist = await this.databaseService.user.findUnique({
          where: {
            email_companyId: {
              email,
              companyId: companyExist?.id,
            },
            isRoot: false,
          },
          include: {
            role: {
              select: {
                name: true,
                RolePrivilege: {
                  select: {
                    privilegeName: true,
                  },
                },
              },
            },
          },
        });
      }

      if (!accountId && !accountExist) {
        throw new NotFoundException('Account user not found');
      }

      console.log(userExist);

      if (accountId && !userExist) {
        throw new BadRequestException('Invalid Credentials');
      }

      const isPassword = await this.cryptoService.compare({
        data: password,
        hash: accountExist ? accountExist?.password : userExist?.password || '',
      });

      if (!isPassword) {
        throw new BadRequestException('Invalid Credentials');
      }

      // TODO: Generate a JWT and return it here
      const access_token = await this.jwtService.signAsync(
        {
          email: accountExist ? accountExist.email : userExist?.email,
          companyId: companyExist.id,
          accountId: accountExist ? accountExist.id : accountId,
          hasAccessToAllBranches: accountExist
            ? 'YES'
            : userExist?.hasAccessToAllBranches,
          roleId: accountExist ? null : userExist?.roleId,
          id: accountExist ? accountExist.id : userExist?.id,
          name: accountExist
            ? accountExist.name
            : userExist?.name || userExist?.email,
        },
        {
          expiresIn: '24h',
        },
      );

      const authUserDetails = accountExist ? accountExist : userExist;
      return {
        ...authUserDetails,
        role: authUserDetails?.role.name,
        roleId: authUserDetails?.roleId,
        privileges: authUserDetails?.role?.RolePrivilege.map(
          (priv) => priv.privilegeName,
        ),
        userId: userExist?.id || null,
        password: '',
        access_token,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async switchCompany({
    payload,
    token,
  }: {
    payload: AuthSwitchDto;
    token: string;
  }) {
    let accountExist:
      | (Account & {
          role: {
            name: string;
            RolePrivilege: {
              privilegeName: string;
            }[];
          };
        })
      | null = null;

    let userExist:
      | (User & {
          role: {
            name: string;
            RolePrivilege: {
              privilegeName: string;
            }[];
          };
        })
      | null = null;

    const { companyId } = payload;

    const decodedToken = await this.authTokenService.decodeToken(token);

    if (!decodedToken.companyId || !decodedToken.accountId) {
      return new BadRequestException('Unauthorized');
    }

    try {
      const prevCompanyExist = await this.databaseService.company.findUnique({
        where: {
          id: decodedToken.companyId,
          accountId: decodedToken.accountId,
        },
      });

      if (!prevCompanyExist) {
        throw new BadRequestException('Company not found');
      }

      const companyExist = await this.databaseService.company.findUnique({
        where: {
          id: companyId,
          accountId: decodedToken.accountId,
        },
      });

      if (!companyExist) {
        throw new BadRequestException('Company not found');
      }

      if (String(decodedToken.id) === String(decodedToken.accountId)) {
        accountExist = await this.databaseService.account.findUnique({
          where: {
            id: decodedToken.accountId,
          },
          include: {
            role: {
              select: {
                name: true,
                RolePrivilege: {
                  select: {
                    privilegeName: true,
                  },
                },
              },
            },
          },
        });

        if (!accountExist) {
          throw new BadRequestException('Account user not found');
        }
      }

      if (String(decodedToken.id) !== String(decodedToken.accountId)) {
        userExist = await this.databaseService.user.findUnique({
          where: {
            id: decodedToken.id,
          },
          include: {
            role: {
              select: {
                name: true,
                RolePrivilege: {
                  select: {
                    privilegeName: true,
                  },
                },
              },
            },
          },
        });

        console.log(userExist);

        if (!userExist) {
          throw new BadRequestException('Invalid Credentials');
        }
      }

      // TODO: Generate a JWT and return it here
      const access_token = await this.jwtService.signAsync(
        {
          email: accountExist ? accountExist.email : userExist?.email,
          companyId: companyExist.id,
          accountId: accountExist ? accountExist.id : decodedToken.accountId,
          hasAccessToAllBranches: accountExist
            ? 'YES'
            : userExist?.hasAccessToAllBranches,
          roleId: accountExist ? null : userExist?.roleId,
          id: accountExist ? accountExist.id : userExist?.id,
          name: accountExist
            ? accountExist.name
            : userExist?.name || userExist?.email,
        },
        {
          expiresIn: '24h',
        },
      );

      const authUserDetails = accountExist ? accountExist : userExist;
      return {
        ...authUserDetails,
        role: authUserDetails?.role.name,
        roleId: authUserDetails?.roleId,
        privileges: authUserDetails?.role?.RolePrivilege.map(
          (priv) => priv.privilegeName,
        ),
        userId: userExist?.id || null,
        password: '',
        access_token,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async initiateForgotPassword(payload: AuthInitiateForgotPasswordDto) {
    const { email, companyId, accountId } = payload;

    let accountExist: Account | null = null;

    let userExist: User | null = null;

    if (!companyId && !accountId) {
      throw new BadRequestException('Missing required field');
    }

    try {
      if (!companyId) {
        accountExist = await this.databaseService.account.findUnique({
          where: {
            id: accountId,
            email,
          },
        });
      } else {
        userExist = await this.databaseService.user.findUnique({
          where: {
            email_companyId: {
              companyId,
              email,
            },
          },
        });
      }

      if (!accountExist && !userExist) {
        throw new BadRequestException(
          'If an account with that email/details exists, you’ll receive an OTP shortly.',
        );
      }

      const otp = await this.otpService.requestOtp({
        email,
        companyId,
      });

      return {
        otpRef: otp.otpRef,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async completeForgotPassword(payload: AuthCompleteForgotPasswordDto) {
    const { email, companyId, accountId, otp, password } = payload;

    let accountExist: Account | null = null;

    let userExist: User | null = null;

    if (!companyId && !accountId) {
      throw new BadRequestException('Missing required field');
    }

    try {
      if (!companyId) {
        accountExist = await this.databaseService.account.findUnique({
          where: {
            id: accountId,
            email,
          },
        });
      } else {
        userExist = await this.databaseService.user.findUnique({
          where: {
            email_companyId: {
              companyId,
              email,
            },
          },
        });
      }

      if (!accountExist && !userExist) {
        throw new BadRequestException('User account not found');
      }

      const otpIsValid = await this.otpService.verifyAuthOtp({
        email,
        code: otp,
        companyId,
      });

      if (!otpIsValid) {
        throw new BadRequestException('Invalid or expired OTP');
      }

      const hashedPassword = await this.cryptoService.hash(password);

      if (accountExist) {
        await this.databaseService.account.update({
          where: {
            id: accountExist.id,
          },
          data: {
            password: hashedPassword,
          },
        });
      } else if (userExist) {
        await this.databaseService.user.update({
          where: {
            id: userExist.id,
          },
          data: {
            password: hashedPassword,
          },
        });
      }

      return;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async changePassword({
    payload,
    token,
  }: {
    payload: AuthChangePasswordDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    console.log(decodedToken);

    // return;

    const { oldPassword, newPassword } = payload;

    if (!decodedToken.accountId && !decodedToken.companyId) {
      throw new BadRequestException('Missing required field');
    }

    const { accountId, companyId, email } = decodedToken;

    let accountExist: Account | null = null;
    let userExist: User | null = null;

    if (String(accountId) === String(decodedToken.id)) {
      accountExist = await this.databaseService.account.findUnique({
        where: {
          id: accountId,
          email,
        },
      });
    } else {
      userExist = await this.databaseService.user.findUnique({
        where: {
          email_companyId: {
            companyId,
            email,
          },
        },
      });
    }

    if (!accountExist && !userExist) {
      throw new BadRequestException('Invalid credentials');
    }

    const currentPasswordHash =
      accountExist?.password || userExist?.password || '';

    const passwordIsValid = await this.cryptoService.compare({
      data: oldPassword,
      hash: currentPasswordHash,
    });

    if (!passwordIsValid) {
      throw new BadRequestException('Old password is incorrect');
    }

    const hashedNewPassword = await this.cryptoService.hash(newPassword);

    if (accountExist) {
      await this.databaseService.account.update({
        where: {
          id: accountExist.id,
        },
        data: {
          password: hashedNewPassword,
        },
      });
    } else if (userExist) {
      await this.databaseService.user.update({
        where: {
          id: userExist.id,
        },
        data: {
          password: hashedNewPassword,
        },
      });
    }

    return;
  }
}
