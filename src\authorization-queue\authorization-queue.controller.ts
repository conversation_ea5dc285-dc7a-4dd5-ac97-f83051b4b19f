import { <PERSON>, Get, Param, <PERSON>, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { SwaggerService } from 'src/swagger/swagger.service';
import { AuthorizationService } from './authorization-queue.service';

@ApiTags('Authorization')
@Controller('authorization')
export class AuthorizationController {
  constructor(private readonly authorizationService: AuthorizationService) {}

  @SwaggerService.applyOperation({
    method: 'get',
    description: 'Get all authorization queue',
    statusCodes: [],
  })
  // @RequirePrivilege('QUEUE|VIEW')
  @Get()
  getAuthorizationQueue(@Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.authorizationService.getAuthorizationQueue(token!);
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Accept authorization request',
    statusCodes: [],
  })
  @Patch('/accept/:id')
  acceptAuthorizationQueue(@Param('id') id: string, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.authorizationService.acceptRequest({
      token: token!,
      queueId: id,
    });
  }

  @SwaggerService.applyOperation({
    method: 'update',
    description: 'Reject authorization request',
    statusCodes: [],
  })
  @Patch('/reject/:id')
  rejectAuthorizationQueue(@Param('id') id: string, @Req() request: Request) {
    const token = request.headers.authorization?.split(' ')[1];

    return this.authorizationService.rejectRequest({
      token: token!,
      queueId: id,
    });
  }
}
